#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');

// 配置加载
const DEBUG_MODE = (process.env.DebugMode || "false").toLowerCase() === "true";
const MAX_CONCURRENT_STEPS = parseInt(process.env.MAX_CONCURRENT_STEPS) || 5;
const WORKFLOW_TIMEOUT = parseInt(process.env.WORKFLOW_TIMEOUT) || 1800; // 30分钟
const ENABLE_STEP_RETRY = (process.env.ENABLE_STEP_RETRY || "true").toLowerCase() === "true";
const MAX_RETRY_ATTEMPTS = parseInt(process.env.MAX_RETRY_ATTEMPTS) || 3;
const CALLBACK_BASE_URL = process.env.CALLBACK_BASE_URL || "http://localhost:6005/plugin-callback";

// 调试日志
function debugLog(message, ...args) {
    if (DEBUG_MODE) {
        console.error(`[WorkflowEngine][Debug] ${message}`, ...args);
    }
}

// 工作流执行器类
class WorkflowExecutor {
    constructor(workflowId, workflowDef) {
        this.workflowId = workflowId;
        this.workflow = workflowDef;
        this.variables = { ...workflowDef.variables || {} };
        this.stepResults = new Map();
        this.stepStatus = new Map();
        this.executionLog = [];
        this.startTime = Date.now();
        
        // 初始化步骤状态
        this.workflow.steps.forEach(step => {
            this.stepStatus.set(step.id, 'pending');
        });
        
        debugLog(`工作流初始化完成: ${workflowId}`, this.workflow);
    }

    // 替换变量占位符
    replaceVariables(text) {
        if (typeof text !== 'string') return text;
        
        return text.replace(/\{\{(\w+)\}\}/g, (match, varName) => {
            if (this.variables.hasOwnProperty(varName)) {
                return this.variables[varName];
            }
            return match; // 保持原样如果变量不存在
        });
    }

    // 检查步骤是否可以执行
    canExecuteStep(step) {
        // 检查依赖
        if (step.depends_on && step.depends_on.length > 0) {
            for (const depId of step.depends_on) {
                if (this.stepStatus.get(depId) !== 'completed') {
                    return false;
                }
            }
        }
        
        // 检查条件
        if (step.condition) {
            // 简单的条件判断实现
            const condition = this.replaceVariables(step.condition);
            try {
                // 这里可以实现更复杂的条件判断逻辑
                return eval(condition);
            } catch (e) {
                debugLog(`条件判断失败: ${step.condition}`, e);
                return false;
            }
        }
        
        return true;
    }

    // 获取可执行的步骤
    getExecutableSteps() {
        return this.workflow.steps.filter(step => 
            this.stepStatus.get(step.id) === 'pending' && 
            this.canExecuteStep(step)
        );
    }

    // 执行单个步骤
    async executeStep(step) {
        debugLog(`开始执行步骤: ${step.id} - ${step.name}`);
        this.stepStatus.set(step.id, 'running');
        
        try {
            // 准备参数，替换变量
            const params = {};
            for (const [key, value] of Object.entries(step.params || {})) {
                if (typeof value === 'string') {
                    params[key] = this.replaceVariables(value);
                } else {
                    params[key] = value;
                }
            }
            
            // 调用VCP插件 (这里需要实现插件调用逻辑)
            const result = await this.callVCPPlugin(step.plugin, params);
            
            // 保存结果
            this.stepResults.set(step.id, result);
            this.stepStatus.set(step.id, 'completed');
            
            // 更新变量
            if (step.output_var && result.result) {
                this.variables[step.output_var] = result.result;
            }
            
            this.executionLog.push({
                timestamp: Date.now(),
                stepId: step.id,
                status: 'completed',
                result: result
            });
            
            debugLog(`步骤执行完成: ${step.id}`, result);
            return result;
            
        } catch (error) {
            debugLog(`步骤执行失败: ${step.id}`, error);
            
            // 重试逻辑
            if (ENABLE_STEP_RETRY && step.retry_on_failure !== false) {
                const retryCount = (step._retryCount || 0) + 1;
                if (retryCount <= MAX_RETRY_ATTEMPTS) {
                    step._retryCount = retryCount;
                    this.stepStatus.set(step.id, 'pending');
                    
                    this.executionLog.push({
                        timestamp: Date.now(),
                        stepId: step.id,
                        status: 'retry',
                        attempt: retryCount,
                        error: error.message
                    });
                    
                    debugLog(`步骤重试: ${step.id}, 第${retryCount}次`);
                    return null; // 返回null表示需要重试
                }
            }
            
            // 标记为失败
            this.stepStatus.set(step.id, 'failed');
            this.executionLog.push({
                timestamp: Date.now(),
                stepId: step.id,
                status: 'failed',
                error: error.message
            });
            
            throw error;
        }
    }

    // 调用VCP插件
    async callVCPPlugin(pluginName, params) {
        debugLog(`调用插件: ${pluginName}`, params);

        try {
            // 构造VCP工具调用格式
            const toolRequest = this.buildToolRequest(pluginName, params);

            // 调用VCP服务器的内部API
            const response = await this.executeVCPTool(toolRequest);

            return {
                status: 'success',
                result: response.result || response,
                plugin: pluginName,
                timestamp: Date.now(),
                raw_response: response
            };

        } catch (error) {
            debugLog(`插件调用失败: ${pluginName}`, error);
            throw new Error(`插件${pluginName}调用失败: ${error.message}`);
        }
    }

    // 构造工具请求格式
    buildToolRequest(pluginName, params) {
        let toolRequest = `<<<[TOOL_REQUEST]>>>\ntool_name:「始」${pluginName}「末」`;

        // 添加参数
        for (const [key, value] of Object.entries(params)) {
            toolRequest += `,\n${key}:「始」${value}「末」`;
        }

        toolRequest += '\n<<<[END_TOOL_REQUEST]>>>';

        return toolRequest;
    }

    // 执行VCP工具 (通过HTTP API调用)
    async executeVCPTool(toolRequest) {
        // 在实际实现中，我们应该通过VCP的内部API调用插件
        // 这里我们使用axios模拟这个过程

        try {
            // 模拟VCP内部API调用
            // 在实际实现中，这里应该是对VCP服务器的内部API调用
            // 例如：const response = await axios.post('http://localhost:6005/internal/execute-tool', { toolRequest });

            // 目前使用模拟实现
            await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 1000));

            // 模拟不同插件的返回结果
            const pluginName = toolRequest.match(/tool_name:「始」([^「]+)「末」/)?.[1] || 'Unknown';
            let result;

            switch(pluginName) {
                case 'TavilySearch':
                    result = {
                        status: 'success',
                        result: `搜索结果: ${toolRequest.includes('市场趋势') ? '发现市场正在快速增长，年增长率约15%。主要竞争对手有Apple Watch、Fitbit等。' : '找到相关信息3条。'}`,
                    };
                    break;

                case 'FluxGen':
                    result = {
                        status: 'success',
                        result: {
                            content: [
                                { type: 'text', text: '图片生成成功！' },
                                {
                                    type: 'image_url',
                                    image_url: { url: 'https://example.com/generated_image.jpg' }
                                }
                            ]
                        }
                    };
                    break;

                case 'SunoGen':
                    result = {
                        status: 'success',
                        result: {
                            audioUrl: 'https://example.com/generated_music.mp3',
                            message: '音乐生成成功！'
                        }
                    };
                    break;

                case 'VideoGenerator':
                    result = {
                        status: 'success',
                        result: {
                            videoUrl: 'https://example.com/generated_video.mp4',
                            message: '视频生成成功！'
                        }
                    };
                    break;

                case 'AgentAssistant':
                    result = {
                        status: 'success',
                        result: `Agent回复: 已完成任务，生成了相关内容。${toolRequest.includes('CopywritingExpert') ? '营销文案已生成，突出了产品的核心优势。' : ''}${toolRequest.includes('ScriptWriter') ? '视频脚本已创建，包含引人入胜的开场和清晰的产品展示。' : ''}`
                    };
                    break;

                default:
                    result = {
                        status: 'success',
                        result: `${pluginName}执行结果: 任务已完成`
                    };
            }

            return result;

        } catch (error) {
            debugLog('VCP工具执行失败:', error);
            throw new Error(`VCP工具执行失败: ${error.message}`);
        }
    }

    // 执行工作流
    async execute() {
        debugLog(`开始执行工作流: ${this.workflowId}`);
        
        const timeout = setTimeout(() => {
            throw new Error(`工作流执行超时: ${this.workflowId}`);
        }, WORKFLOW_TIMEOUT * 1000);
        
        try {
            while (true) {
                const executableSteps = this.getExecutableSteps();
                
                if (executableSteps.length === 0) {
                    // 检查是否所有步骤都完成
                    const allCompleted = Array.from(this.stepStatus.values())
                        .every(status => status === 'completed' || status === 'failed');
                    
                    if (allCompleted) {
                        break; // 工作流完成
                    }
                    
                    // 等待一段时间后重试
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    continue;
                }
                
                // 限制并发执行数量
                const stepsToExecute = executableSteps.slice(0, MAX_CONCURRENT_STEPS);
                
                // 并行执行步骤
                const promises = stepsToExecute.map(step => this.executeStep(step));
                await Promise.allSettled(promises);
            }
            
            clearTimeout(timeout);
            
            // 生成执行报告
            const report = this.generateExecutionReport();
            debugLog(`工作流执行完成: ${this.workflowId}`, report);
            
            return report;
            
        } catch (error) {
            clearTimeout(timeout);
            debugLog(`工作流执行失败: ${this.workflowId}`, error);
            throw error;
        }
    }

    // 生成执行报告
    generateExecutionReport() {
        const endTime = Date.now();
        const duration = endTime - this.startTime;
        
        const completedSteps = Array.from(this.stepStatus.entries())
            .filter(([_, status]) => status === 'completed').length;
        const failedSteps = Array.from(this.stepStatus.entries())
            .filter(([_, status]) => status === 'failed').length;
        
        return {
            workflowId: this.workflowId,
            workflowName: this.workflow.name,
            status: failedSteps > 0 ? 'failed' : 'completed',
            duration: duration,
            totalSteps: this.workflow.steps.length,
            completedSteps: completedSteps,
            failedSteps: failedSteps,
            finalVariables: this.variables,
            executionLog: this.executionLog,
            stepResults: Object.fromEntries(this.stepResults),
            message: failedSteps > 0 
                ? `工作流执行失败，${completedSteps}/${this.workflow.steps.length}步骤完成`
                : `工作流执行成功，耗时${Math.round(duration/1000)}秒`
        };
    }
}

// 主函数
async function main() {
    try {
        // 读取输入
        const input = await new Promise((resolve) => {
            let data = '';
            process.stdin.on('data', chunk => data += chunk);
            process.stdin.on('end', () => resolve(data.trim()));
        });
        
        debugLog('接收到输入:', input);
        
        // 解析输入参数
        const params = JSON.parse(input);
        const workflowDef = JSON.parse(params.workflow);
        
        // 生成工作流ID
        const workflowId = uuidv4();
        
        // 立即返回初始响应
        const initialResponse = {
            status: "success",
            result: {
                workflowId: workflowId,
                workflowName: workflowDef.name,
                message: `工作流"${workflowDef.name}"已提交，正在后台执行中...`,
                totalSteps: workflowDef.steps.length,
                estimatedDuration: `预计${Math.ceil(workflowDef.steps.length * 30 / 60)}分钟`
            },
            messageForAI: `工作流"${workflowDef.name}"(ID: ${workflowId})已成功提交执行。包含${workflowDef.steps.length}个步骤，预计执行时间${Math.ceil(workflowDef.steps.length * 30 / 60)}分钟。请告知用户耐心等待，执行完成后会收到通知。可以使用占位符 {{VCP_ASYNC_RESULT::WorkflowAutomationEngine::${workflowId}}} 来获取最终结果。`
        };
        
        console.log(JSON.stringify(initialResponse));
        
        // 在后台执行工作流
        setImmediate(async () => {
            try {
                const executor = new WorkflowExecutor(workflowId, workflowDef);
                const result = await executor.execute();
                
                // 回调服务器
                await axios.post(`${CALLBACK_BASE_URL}/WorkflowAutomationEngine/${workflowId}`, result);
                
            } catch (error) {
                debugLog('工作流执行异常:', error);
                
                // 发送失败回调
                const failureResult = {
                    workflowId: workflowId,
                    workflowName: workflowDef.name,
                    status: 'failed',
                    error: error.message,
                    message: `工作流"${workflowDef.name}"执行失败: ${error.message}`
                };
                
                try {
                    await axios.post(`${CALLBACK_BASE_URL}/WorkflowAutomationEngine/${workflowId}`, failureResult);
                } catch (callbackError) {
                    debugLog('回调失败:', callbackError);
                }
            }
        });
        
    } catch (error) {
        console.error('插件执行错误:', error);
        console.log(JSON.stringify({
            status: "error",
            error: error.message,
            messageForAI: `工作流引擎执行失败: ${error.message}`
        }));
        process.exit(1);
    }
}

// 启动主函数
if (require.main === module) {
    main();
}

module.exports = { WorkflowExecutor };
