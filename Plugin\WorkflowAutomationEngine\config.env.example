# WorkflowAutomationEngine 插件配置文件
# 请复制此文件为 config.env 并根据需要调整配置

# ===== 执行控制配置 =====
# 最大并发执行步骤数 (建议根据服务器性能调整)
MAX_CONCURRENT_STEPS=5

# 工作流执行超时时间(秒) - 30分钟
WORKFLOW_TIMEOUT=1800

# 是否启用步骤重试机制
ENABLE_STEP_RETRY=true

# 最大重试次数
MAX_RETRY_ATTEMPTS=3

# ===== 调试配置 =====
# 是否启用详细调试日志
DebugMode=false

# ===== 高级配置 =====
# 步骤执行间隔(毫秒) - 避免过于频繁的插件调用
STEP_EXECUTION_INTERVAL=500

# 工作流结果保存天数
RESULT_RETENTION_DAYS=7

# 是否启用工作流执行统计
ENABLE_EXECUTION_STATS=true

# ===== 通知配置 =====
# 是否启用执行进度通知
ENABLE_PROGRESS_NOTIFICATIONS=true

# 进度通知间隔(步骤数)
PROGRESS_NOTIFICATION_INTERVAL=3
