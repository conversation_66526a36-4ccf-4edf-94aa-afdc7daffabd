# WorkflowAutomationEngine - AI工作流自动化引擎

## 🌟 插件简介

WorkflowAutomationEngine 是一个强大的VCP插件，让AI能够设计和执行复杂的多步骤工作流。它支持插件协调、并行执行、条件判断和变量传递，真正实现了AI的自主任务编排能力。

## ✨ 核心特性

- 🔄 **并行执行**: 支持多个步骤同时执行，提高效率
- 📊 **依赖管理**: 智能处理步骤间的依赖关系
- 🔀 **变量传递**: 在步骤间无缝传递和转换数据
- ⚡ **条件执行**: 基于条件动态决定步骤执行
- 🔁 **错误重试**: 自动重试失败的步骤
- 📈 **实时监控**: 提供详细的执行进度和状态信息
- 🎯 **异步执行**: 支持长时间运行的复杂工作流

## 🚀 快速开始

### 安装依赖

```bash
cd plugin/WorkflowAutomationEngine
npm install
```

### 配置插件

1. 复制配置文件模板：
```bash
cp config.env.example config.env
```

2. 根据需要调整配置参数

### 基本使用

AI可以通过以下格式调用工作流引擎：

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WorkflowAutomationEngine「末」,
workflow:「始」{
  "name": "示例工作流",
  "description": "一个简单的示例工作流",
  "variables": {
    "product_name": "智能手表"
  },
  "steps": [
    {
      "id": "step1",
      "name": "市场调研",
      "plugin": "TavilySearch",
      "params": {
        "query": "{{product_name}} 市场趋势"
      },
      "output_var": "research_data"
    },
    {
      "id": "step2", 
      "name": "生成海报",
      "plugin": "FluxGen",
      "params": {
        "prompt": "为{{product_name}}设计海报"
      },
      "depends_on": ["step1"],
      "output_var": "poster"
    }
  ]
}「末」
<<<[END_TOOL_REQUEST]>>>
```

## 📋 工作流定义格式

### 基本结构

```json
{
  "name": "工作流名称",
  "description": "工作流描述",
  "variables": {
    "变量名": "初始值"
  },
  "steps": [
    {
      "id": "唯一步骤ID",
      "name": "步骤显示名称",
      "plugin": "要调用的VCP插件名",
      "params": {
        "参数名": "参数值或{{变量名}}"
      },
      "depends_on": ["前置步骤ID"],
      "parallel_with": ["可并行步骤ID"],
      "output_var": "输出变量名",
      "condition": "执行条件表达式",
      "retry_on_failure": true
    }
  ]
}
```

### 字段说明

- **name**: 工作流名称，用于显示和日志
- **description**: 工作流描述
- **variables**: 全局变量定义，可在步骤中使用
- **steps**: 步骤数组，每个步骤包含：
  - **id**: 唯一标识符
  - **name**: 显示名称
  - **plugin**: 要调用的VCP插件名
  - **params**: 传递给插件的参数
  - **depends_on**: 依赖的前置步骤ID数组
  - **parallel_with**: 可以并行执行的步骤ID数组
  - **output_var**: 将步骤结果保存到的变量名
  - **condition**: 执行条件（可选）
  - **retry_on_failure**: 是否在失败时重试（可选）

## 🔧 高级功能

### 变量系统

工作流支持强大的变量系统：

```json
{
  "variables": {
    "user_name": "张三",
    "product_type": "智能设备"
  },
  "steps": [
    {
      "params": {
        "message": "你好 {{user_name}}，为您推荐 {{product_type}}"
      }
    }
  ]
}
```

### 条件执行

支持基于变量的条件判断：

```json
{
  "steps": [
    {
      "id": "conditional_step",
      "condition": "{{user_type}} === 'premium'",
      "plugin": "PremiumService"
    }
  ]
}
```

### 并行执行

多个步骤可以并行执行以提高效率：

```json
{
  "steps": [
    {
      "id": "image_gen",
      "plugin": "FluxGen",
      "parallel_with": ["music_gen"]
    },
    {
      "id": "music_gen", 
      "plugin": "SunoGen",
      "parallel_with": ["image_gen"]
    }
  ]
}
```

## 📊 监控和调试

### 执行状态

工作流执行过程中会提供详细的状态信息：

- **pending**: 等待执行
- **running**: 正在执行
- **completed**: 执行完成
- **failed**: 执行失败

### 调试模式

在 `config.env` 中设置 `DebugMode=true` 可以启用详细的调试日志。

## 🔄 错误处理

插件提供了完善的错误处理机制：

1. **自动重试**: 失败的步骤会自动重试
2. **错误隔离**: 单个步骤失败不会影响其他步骤
3. **详细日志**: 提供完整的错误信息和执行日志

## 📈 性能优化

- **并发控制**: 限制同时执行的步骤数量
- **超时管理**: 防止工作流无限期执行
- **资源监控**: 监控内存和CPU使用情况

## 🛠️ 开发和扩展

### 添加新的插件支持

工作流引擎可以调用任何VCP插件，只需在步骤中指定正确的插件名称和参数。

### 自定义条件判断

可以扩展条件判断逻辑以支持更复杂的业务规则。

## 📝 使用示例

查看 `examples/` 目录中的完整示例工作流定义。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个插件！

## 📄 许可证

本插件遵循 CC BY-NC-SA 4.0 许可证。
