{"name": "产品营销内容生成工作流", "description": "自动生成完整的产品营销内容套件，包括市场调研、宣传海报、背景音乐和宣传视频", "variables": {"product_name": "智能手表X1", "target_audience": "年轻专业人士", "key_features": "健康监测、智能通知、长续航", "brand_style": "现代简约"}, "steps": [{"id": "market_research", "name": "市场调研", "plugin": "<PERSON><PERSON>Sear<PERSON>", "params": {"query": "{{product_name}} 类似产品 市场趋势 {{target_audience}} 消费习惯", "num_results": 5}, "output_var": "market_data"}, {"id": "competitor_analysis", "name": "竞品分析", "plugin": "<PERSON><PERSON>Sear<PERSON>", "params": {"query": "{{product_name}} 竞品分析 优缺点对比", "num_results": 3}, "output_var": "competitor_data", "parallel_with": ["market_research"]}, {"id": "marketing_copy", "name": "营销文案生成", "plugin": "AgentAssistant", "params": {"agent_name": "CopywritingExpert", "task": "基于以下市场调研和竞品分析，为{{product_name}}创作吸引{{target_audience}}的营销文案。突出产品特点：{{key_features}}。\n\n市场调研：{{market_data}}\n\n竞品分析：{{competitor_data}}"}, "depends_on": ["market_research", "competitor_analysis"], "output_var": "marketing_copy"}, {"id": "poster_design", "name": "宣传海报设计", "plugin": "FluxGen", "params": {"prompt": "为{{product_name}}设计一张{{brand_style}}风格的宣传海报。产品特点：{{key_features}}。目标受众：{{target_audience}}。", "size": "1024x1024", "style": "{{brand_style}}"}, "depends_on": ["marketing_copy"], "output_var": "poster_image"}, {"id": "background_music", "name": "背景音乐创作", "plugin": "SunoGen", "params": {"prompt": "为{{product_name}}创作一段现代、充满活力的背景音乐，适合{{target_audience}}的品味。", "style": "electronic", "duration": "medium"}, "parallel_with": ["poster_design"], "depends_on": ["marketing_copy"], "output_var": "background_music"}, {"id": "video_script", "name": "视频脚本生成", "plugin": "AgentAssistant", "params": {"agent_name": "ScriptWriter", "task": "为{{product_name}}创作一个30秒宣传视频的脚本。目标受众：{{target_audience}}。产品特点：{{key_features}}。使用以下营销文案作为基础：{{marketing_copy}}"}, "depends_on": ["marketing_copy"], "output_var": "video_script"}, {"id": "promotional_video", "name": "宣传视频生成", "plugin": "VideoGenerator", "params": {"prompt": "基于以下脚本创建一个产品宣传视频：{{video_script}}", "image": "{{poster_image}}", "audio": "{{background_music}}", "duration": 30}, "depends_on": ["poster_design", "background_music", "video_script"], "output_var": "promo_video", "retry_on_failure": true}, {"id": "final_report", "name": "营销资料汇总", "plugin": "AgentAssistant", "params": {"agent_name": "MarketingManager", "task": "为{{product_name}}的营销活动创建一份完整报告，包括市场分析、营销文案、视觉设计和视频资料的链接。\n\n市场数据：{{market_data}}\n\n竞品分析：{{competitor_data}}\n\n营销文案：{{marketing_copy}}\n\n海报：[图片链接]\n\n背景音乐：[音频链接]\n\n宣传视频：[视频链接]"}, "depends_on": ["promotional_video"], "output_var": "marketing_report"}]}