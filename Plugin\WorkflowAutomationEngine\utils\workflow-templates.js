// 工作流模板生成器
// 提供常用的工作流模板，方便AI快速构建工作流

const workflowTemplates = {
    // 内容创作工作流
    contentCreation: {
        name: "内容创作工作流",
        description: "自动化内容创作流程，包括调研、写作、设计和发布",
        variables: {
            topic: "请填写主题",
            target_audience: "请填写目标受众",
            content_type: "请填写内容类型"
        },
        steps: [
            {
                id: "research",
                name: "主题调研",
                plugin: "TavilySearch",
                params: {
                    query: "{{topic}} 最新趋势 {{target_audience}} 关注点"
                },
                output_var: "research_data"
            },
            {
                id: "content_writing",
                name: "内容创作",
                plugin: "AgentAssistant",
                params: {
                    agent_name: "ContentWriter",
                    task: "基于调研数据{{research_data}}，为{{target_audience}}创作关于{{topic}}的{{content_type}}"
                },
                depends_on: ["research"],
                output_var: "content"
            },
            {
                id: "visual_design",
                name: "视觉设计",
                plugin: "FluxGen",
                params: {
                    prompt: "为{{content_type}}设计配图，主题：{{topic}}"
                },
                parallel_with: ["content_writing"],
                output_var: "visual"
            }
        ]
    },

    // 产品发布工作流
    productLaunch: {
        name: "产品发布工作流",
        description: "完整的产品发布流程，从市场分析到营销材料制作",
        variables: {
            product_name: "请填写产品名称",
            key_features: "请填写核心功能",
            target_market: "请填写目标市场"
        },
        steps: [
            {
                id: "market_analysis",
                name: "市场分析",
                plugin: "TavilySearch",
                params: {
                    query: "{{product_name}} 市场分析 {{target_market}} 竞品"
                },
                output_var: "market_analysis"
            },
            {
                id: "positioning",
                name: "产品定位",
                plugin: "AgentAssistant",
                params: {
                    agent_name: "MarketingStrategist",
                    task: "基于市场分析{{market_analysis}}，为{{product_name}}制定产品定位策略"
                },
                depends_on: ["market_analysis"],
                output_var: "positioning"
            },
            {
                id: "marketing_copy",
                name: "营销文案",
                plugin: "AgentAssistant",
                params: {
                    agent_name: "Copywriter",
                    task: "基于产品定位{{positioning}}，为{{product_name}}创作营销文案，突出{{key_features}}"
                },
                depends_on: ["positioning"],
                output_var: "copy"
            },
            {
                id: "visual_assets",
                name: "视觉资产",
                plugin: "FluxGen",
                params: {
                    prompt: "为{{product_name}}设计产品海报，体现{{key_features}}"
                },
                depends_on: ["marketing_copy"],
                output_var: "visuals"
            },
            {
                id: "promo_video",
                name: "宣传视频",
                plugin: "VideoGenerator",
                params: {
                    prompt: "基于营销文案{{copy}}制作产品宣传视频",
                    image: "{{visuals}}"
                },
                depends_on: ["visual_assets"],
                output_var: "video"
            }
        ]
    },

    // 学习研究工作流
    researchStudy: {
        name: "学习研究工作流",
        description: "系统化的学习研究流程，包括资料收集、分析和总结",
        variables: {
            research_topic: "请填写研究主题",
            depth_level: "基础/中级/高级",
            output_format: "报告/PPT/思维导图"
        },
        steps: [
            {
                id: "literature_search",
                name: "文献搜索",
                plugin: "TavilySearch",
                params: {
                    query: "{{research_topic}} 最新研究 学术论文"
                },
                output_var: "literature"
            },
            {
                id: "academic_papers",
                name: "学术论文检索",
                plugin: "ArxivDailyPapers",
                params: {
                    query: "{{research_topic}}"
                },
                parallel_with: ["literature_search"],
                output_var: "papers"
            },
            {
                id: "content_analysis",
                name: "内容分析",
                plugin: "AgentAssistant",
                params: {
                    agent_name: "ResearchAnalyst",
                    task: "分析以下资料，提取关于{{research_topic}}的核心观点：\n文献：{{literature}}\n论文：{{papers}}"
                },
                depends_on: ["literature_search", "academic_papers"],
                output_var: "analysis"
            },
            {
                id: "knowledge_synthesis",
                name: "知识综合",
                plugin: "AgentAssistant",
                params: {
                    agent_name: "KnowledgeOrganizer",
                    task: "将分析结果{{analysis}}整理成{{depth_level}}水平的{{output_format}}格式"
                },
                depends_on: ["content_analysis"],
                output_var: "final_output"
            }
        ]
    },

    // 数据分析工作流
    dataAnalysis: {
        name: "数据分析工作流",
        description: "自动化数据分析流程，从数据收集到可视化报告",
        variables: {
            data_source: "请填写数据来源",
            analysis_goal: "请填写分析目标",
            chart_type: "请填写图表类型"
        },
        steps: [
            {
                id: "data_collection",
                name: "数据收集",
                plugin: "UrlFetch",
                params: {
                    url: "{{data_source}}",
                    mode: "direct"
                },
                output_var: "raw_data"
            },
            {
                id: "data_processing",
                name: "数据处理",
                plugin: "SciCalculator",
                params: {
                    expression: "统计分析: {{raw_data}}"
                },
                depends_on: ["data_collection"],
                output_var: "processed_data"
            },
            {
                id: "visualization",
                name: "数据可视化",
                plugin: "FluxGen",
                params: {
                    prompt: "创建{{chart_type}}图表，展示数据分析结果"
                },
                depends_on: ["data_processing"],
                output_var: "charts"
            },
            {
                id: "report_generation",
                name: "报告生成",
                plugin: "AgentAssistant",
                params: {
                    agent_name: "DataAnalyst",
                    task: "基于处理后的数据{{processed_data}}和图表{{charts}}，生成数据分析报告，目标：{{analysis_goal}}"
                },
                depends_on: ["visualization"],
                output_var: "final_report"
            }
        ]
    },

    // 社交媒体营销工作流
    socialMediaMarketing: {
        name: "社交媒体营销工作流",
        description: "多平台社交媒体内容创作和发布流程",
        variables: {
            brand_name: "请填写品牌名称",
            campaign_theme: "请填写活动主题",
            platforms: "请填写目标平台"
        },
        steps: [
            {
                id: "trend_analysis",
                name: "趋势分析",
                plugin: "DailyHot",
                params: {},
                output_var: "trends"
            },
            {
                id: "content_strategy",
                name: "内容策略",
                plugin: "AgentAssistant",
                params: {
                    agent_name: "SocialMediaManager",
                    task: "基于当前热点{{trends}}，为{{brand_name}}制定{{campaign_theme}}的社交媒体内容策略"
                },
                depends_on: ["trend_analysis"],
                output_var: "strategy"
            },
            {
                id: "visual_content",
                name: "视觉内容",
                plugin: "FluxGen",
                params: {
                    prompt: "为{{brand_name}}的{{campaign_theme}}活动创作社交媒体配图"
                },
                depends_on: ["content_strategy"],
                output_var: "images"
            },
            {
                id: "copy_writing",
                name: "文案创作",
                plugin: "AgentAssistant",
                params: {
                    agent_name: "SocialCopywriter",
                    task: "基于内容策略{{strategy}}，为{{platforms}}平台创作{{campaign_theme}}相关文案"
                },
                parallel_with: ["visual_content"],
                output_var: "copy"
            },
            {
                id: "posting_schedule",
                name: "发布计划",
                plugin: "AgentAssistant",
                params: {
                    agent_name: "SocialScheduler",
                    task: "制定内容发布时间表，整合文案{{copy}}和图片{{images}}"
                },
                depends_on: ["visual_content", "copy_writing"],
                output_var: "schedule"
            }
        ]
    }
};

// 模板生成器函数
function generateWorkflowFromTemplate(templateName, customVariables = {}) {
    const template = workflowTemplates[templateName];
    if (!template) {
        throw new Error(`模板 "${templateName}" 不存在`);
    }

    // 深拷贝模板
    const workflow = JSON.parse(JSON.stringify(template));
    
    // 合并自定义变量
    workflow.variables = { ...workflow.variables, ...customVariables };
    
    return workflow;
}

// 获取所有可用模板
function getAvailableTemplates() {
    return Object.keys(workflowTemplates).map(key => ({
        name: key,
        displayName: workflowTemplates[key].name,
        description: workflowTemplates[key].description,
        variableCount: Object.keys(workflowTemplates[key].variables).length,
        stepCount: workflowTemplates[key].steps.length
    }));
}

module.exports = {
    workflowTemplates,
    generateWorkflowFromTemplate,
    getAvailableTemplates
};
