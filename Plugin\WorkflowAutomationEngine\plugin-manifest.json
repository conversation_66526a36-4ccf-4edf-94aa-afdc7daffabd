{"manifestVersion": "1.0.0", "name": "WorkflowAutomationEngine", "version": "1.0.0", "displayName": "AI工作流自动化引擎", "description": "让AI能够设计和执行复杂的多步骤工作流，支持插件协调、并行执行、条件判断和变量传递。实现AI的自主任务编排能力。", "author": "VCP-Assistant", "pluginType": "asynchronous", "entryPoint": {"type": "nodejs", "command": "node workflow-engine.js"}, "communication": {"protocol": "stdio", "timeout": 300000}, "configSchema": {"MAX_CONCURRENT_STEPS": {"type": "integer", "description": "最大并发执行步骤数", "default": 5, "required": false}, "WORKFLOW_TIMEOUT": {"type": "integer", "description": "工作流执行超时时间(秒)", "default": 1800, "required": false}, "ENABLE_STEP_RETRY": {"type": "boolean", "description": "是否启用步骤重试机制", "default": true, "required": false}, "MAX_RETRY_ATTEMPTS": {"type": "integer", "description": "最大重试次数", "default": 3, "required": false}, "DebugMode": {"type": "boolean", "description": "是否启用详细调试日志", "default": false, "required": false}}, "capabilities": {"invocationCommands": [{"commandIdentifier": "ExecuteWorkflow", "description": "执行AI设计的工作流程。支持多步骤任务编排、并行执行、条件判断和变量传递。\n\n**工作流定义格式**:\n```json\n{\n  \"name\": \"工作流名称\",\n  \"description\": \"工作流描述\", \n  \"variables\": {\n    \"var_name\": \"初始值\"\n  },\n  \"steps\": [\n    {\n      \"id\": \"step1\",\n      \"name\": \"步骤名称\",\n      \"plugin\": \"插件名称\",\n      \"params\": {\n        \"param1\": \"{{variable_name}}\",\n        \"param2\": \"固定值\"\n      },\n      \"depends_on\": [\"前置步骤ID\"],\n      \"parallel_with\": [\"并行步骤ID\"],\n      \"output_var\": \"输出变量名\",\n      \"condition\": \"执行条件(可选)\",\n      \"retry_on_failure\": true\n    }\n  ]\n}\n```\n\n**支持的特性**:\n- 🔄 **并行执行**: 使用 `parallel_with` 指定可并行的步骤\n- 📊 **依赖管理**: 使用 `depends_on` 指定前置依赖\n- 🔀 **变量传递**: 使用 `{{variable_name}}` 在步骤间传递数据\n- ⚡ **条件执行**: 使用 `condition` 字段进行条件判断\n- 🔁 **错误重试**: 支持失败步骤的自动重试\n- 📈 **实时监控**: 提供详细的执行进度和状态信息\n\n**调用格式**:\n```\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」WorkflowAutomationEngine「末」,\nworkflow:「始」{工作流JSON定义}「末」\n<<<[END_TOOL_REQUEST]>>>\n```\n\n**返回信息**: 插件会立即返回工作流ID和初始状态，然后在后台异步执行。执行完成后通过WebSocket推送最终结果。", "example": "```\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」WorkflowAutomationEngine「末」,\nworkflow:「始」{\n  \"name\": \"产品宣传内容制作\",\n  \"description\": \"为新产品制作完整的宣传内容\",\n  \"variables\": {\n    \"product_name\": \"智能手表X1\",\n    \"target_audience\": \"年轻专业人士\"\n  },\n  \"steps\": [\n    {\n      \"id\": \"research\",\n      \"name\": \"市场调研\", \n      \"plugin\": \"TavilySearch\",\n      \"params\": {\n        \"query\": \"{{product_name}} 市场趋势 竞品分析\"\n      },\n      \"output_var\": \"market_data\"\n    },\n    {\n      \"id\": \"poster\",\n      \"name\": \"生成宣传海报\",\n      \"plugin\": \"FluxGen\", \n      \"params\": {\n        \"prompt\": \"为{{product_name}}设计现代简约风格海报，目标用户{{target_audience}}\"\n      },\n      \"depends_on\": [\"research\"],\n      \"output_var\": \"poster_image\"\n    },\n    {\n      \"id\": \"music\",\n      \"name\": \"创作背景音乐\",\n      \"plugin\": \"SunoGen\",\n      \"params\": {\n        \"prompt\": \"为{{product_name}}创作现代电子风格背景音乐\",\n        \"style\": \"electronic\"\n      },\n      \"parallel_with\": [\"poster\"],\n      \"output_var\": \"bg_music\"\n    }\n  ]\n}「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}, "webSocketPush": {"enabled": true, "usePluginResultAsMessage": true, "targetClientType": "WorkflowMonitor"}}