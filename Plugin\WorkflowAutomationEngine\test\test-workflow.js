#!/usr/bin/env node

const { WorkflowExecutor } = require('../workflow-engine.js');

// 测试工作流定义
const testWorkflow = {
    name: "测试工作流",
    description: "用于测试工作流引擎的基本功能",
    variables: {
        product_name: "智能手表X1",
        target_audience: "年轻专业人士"
    },
    steps: [
        {
            id: "step1",
            name: "市场调研",
            plugin: "TavilySearch",
            params: {
                query: "{{product_name}} 市场趋势 {{target_audience}}"
            },
            output_var: "market_data"
        },
        {
            id: "step2",
            name: "生成海报",
            plugin: "FluxGen",
            params: {
                prompt: "为{{product_name}}设计现代简约风格海报，目标用户{{target_audience}}"
            },
            depends_on: ["step1"],
            output_var: "poster_image"
        },
        {
            id: "step3",
            name: "创作音乐",
            plugin: "SunoGen",
            params: {
                prompt: "为{{product_name}}创作现代电子风格背景音乐"
            },
            parallel_with: ["step2"],
            output_var: "background_music"
        },
        {
            id: "step4",
            name: "生成视频",
            plugin: "VideoGenerator",
            params: {
                prompt: "基于市场调研{{market_data}}创建产品宣传视频",
                image: "{{poster_image}}",
                audio: "{{background_music}}"
            },
            depends_on: ["step2", "step3"],
            output_var: "final_video"
        }
    ]
};

async function runTest() {
    console.log('🚀 开始测试工作流引擎...\n');
    
    try {
        // 创建工作流执行器
        const executor = new WorkflowExecutor('test-workflow-001', testWorkflow);
        
        console.log('📋 工作流定义:');
        console.log(`名称: ${testWorkflow.name}`);
        console.log(`步骤数: ${testWorkflow.steps.length}`);
        console.log(`初始变量: ${JSON.stringify(testWorkflow.variables, null, 2)}\n`);
        
        // 执行工作流
        console.log('⚡ 开始执行工作流...\n');
        const startTime = Date.now();
        
        const result = await executor.execute();
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log('✅ 工作流执行完成!\n');
        console.log('📊 执行结果:');
        console.log(`状态: ${result.status}`);
        console.log(`耗时: ${Math.round(duration/1000)}秒`);
        console.log(`完成步骤: ${result.completedSteps}/${result.totalSteps}`);
        console.log(`失败步骤: ${result.failedSteps}`);
        
        if (result.status === 'completed') {
            console.log('\n🎉 所有步骤执行成功!');
            console.log('\n📝 最终变量:');
            console.log(JSON.stringify(result.finalVariables, null, 2));
            
            console.log('\n📋 步骤结果:');
            for (const [stepId, stepResult] of Object.entries(result.stepResults)) {
                console.log(`- ${stepId}: ${stepResult.status}`);
            }
        } else {
            console.log('\n❌ 工作流执行失败');
            console.log('错误详情:', result.message);
        }
        
        console.log('\n📜 执行日志:');
        result.executionLog.forEach((log, index) => {
            const time = new Date(log.timestamp).toLocaleTimeString();
            console.log(`${index + 1}. [${time}] ${log.stepId}: ${log.status}`);
            if (log.error) {
                console.log(`   错误: ${log.error}`);
            }
        });
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
    }
}

// 测试变量替换功能
function testVariableReplacement() {
    console.log('\n🔧 测试变量替换功能...');
    
    const executor = new WorkflowExecutor('test-var', {
        variables: {
            name: "张三",
            product: "智能手表",
            count: 5
        }
    });
    
    const testCases = [
        "你好 {{name}}",
        "产品名称: {{product}}",
        "数量: {{count}} 个",
        "复合: {{name}} 购买了 {{count}} 个 {{product}}",
        "未定义变量: {{undefined_var}}"
    ];
    
    testCases.forEach(testCase => {
        const result = executor.replaceVariables(testCase);
        console.log(`输入: "${testCase}"`);
        console.log(`输出: "${result}"`);
        console.log('---');
    });
}

// 运行测试
if (require.main === module) {
    console.log('🧪 WorkflowAutomationEngine 测试套件\n');
    
    // 测试变量替换
    testVariableReplacement();
    
    // 测试完整工作流
    runTest().then(() => {
        console.log('\n✨ 测试完成!');
        process.exit(0);
    }).catch(error => {
        console.error('\n💥 测试异常:', error);
        process.exit(1);
    });
}
